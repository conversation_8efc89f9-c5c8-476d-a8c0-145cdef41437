<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuDataModemMapper">

    <resultMap type="BuDataModem" id="BuDataModemResult">
        <result property="id" column="id"/>
        <result property="deviceId" column="device_id"/>
        <result property="deviceCode" column="device_code"/>
        <result property="deviceName" column="device_name"/>
        <result property="initialTime" column="initial_time"/>
        <result property="initialBjTime" column="initial_bj_time"/>
        <result property="status" column="status"/>
        <result property="signal" column="signal"/>
        <result property="speed" column="speed"/>
        <result property="sendPower" column="send_power"/>
        <result property="isFlag" column="is_flag"/>
        <result property="deviceStatus" column="device_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectBuDataModemVo">
        select id,
               device_id,
               device_code,
               device_name,
               initial_time,
               initial_bj_time,
               status,
               signal,
               speed,
               send_power,
               is_flag,
               device_status,
               create_time,
               update_time
        from bu_data_modem
    </sql>

    <select id="selectBuDataModemList" parameterType="BuDataModem" resultMap="BuDataModemResult">
        <include refid="selectBuDataModemVo"/>
        <where>
            <if test="deviceId != null ">and device_id = #{deviceId}</if>
            <if test="deviceCode != null  and deviceCode != ''">and device_code = #{deviceCode}</if>
            <if test="deviceName != null  and deviceName != ''">and device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="initialTime != null ">and initial_time = #{initialTime}</if>
            <if test="initialBjTime != null ">and initial_bj_time = #{initialBjTime}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="signal != null ">and signal = #{signal}</if>
            <if test="speed != null ">and speed = #{speed}</if>
            <if test="sendPower != null ">and send_power = #{sendPower}</if>
            <if test="isFlag != null ">and is_flag = #{isFlag}</if>
            <if test="deviceStatus != null ">and device_status = #{deviceStatus}</if>
        </where>
    </select>

    <select id="selectBuDataModemById" parameterType="Long" resultMap="BuDataModemResult">
        <include refid="selectBuDataModemVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuDataModem" parameterType="BuDataModem" useGeneratedKeys="true" keyProperty="id">
        insert into bu_data_modem
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="deviceCode != null and deviceCode != ''">device_code,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="initialTime != null">initial_time,</if>
            <if test="initialBjTime != null">initial_bj_time,</if>
            <if test="status != null">status,</if>
            <if test="signal != null">signal,</if>
            <if test="speed != null">speed,</if>
            <if test="sendPower != null">send_power,</if>
            <if test="isFlag != null">is_flag,</if>
            <if test="deviceStatus != null">device_status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceCode != null and deviceCode != ''">#{deviceCode},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="initialTime != null">#{initialTime},</if>
            <if test="initialBjTime != null">#{initialBjTime},</if>
            <if test="status != null">#{status},</if>
            <if test="signal != null">#{signal},</if>
            <if test="speed != null">#{speed},</if>
            <if test="sendPower != null">#{sendPower},</if>
            <if test="isFlag != null">#{isFlag},</if>
            <if test="deviceStatus != null">#{deviceStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateBuDataModem" parameterType="BuDataModem">
        update bu_data_modem
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceCode != null and deviceCode != ''">device_code = #{deviceCode},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="initialTime != null">initial_time = #{initialTime},</if>
            <if test="initialBjTime != null">initial_bj_time = #{initialBjTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="signal != null">signal = #{signal},</if>
            <if test="speed != null">speed = #{speed},</if>
            <if test="sendPower != null">send_power = #{sendPower},</if>
            <if test="isFlag != null">is_flag = #{isFlag},</if>
            <if test="deviceStatus != null">device_status = #{deviceStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuDataModemById" parameterType="Long">
        delete
        from bu_data_modem
        where id = #{id}
    </delete>

    <delete id="deleteBuDataModemByIds" parameterType="String">
        delete from bu_data_modem where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>