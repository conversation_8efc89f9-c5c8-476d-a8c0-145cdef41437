package com.snct.system.mapper;

import java.util.List;

import com.snct.system.domain.data.BuDataAws;

/**
 * 气象站设备数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface BuDataAwsMapper {
    /**
     * 查询气象站设备数据
     *
     * @param id 气象站设备数据主键
     * @return 气象站设备数据
     */
    public BuDataAws selectBuDataAwsById(Long id);

    /**
     * 查询气象站设备数据列表
     *
     * @param buDataAws 气象站设备数据
     * @return 气象站设备数据集合
     */
    public List<BuDataAws> selectBuDataAwsList(BuDataAws buDataAws);

    /**
     * 新增气象站设备数据
     *
     * @param buDataAws 气象站设备数据
     * @return 结果
     */
    public int insertBuDataAws(BuDataAws buDataAws);

    /**
     * 修改气象站设备数据
     *
     * @param buDataAws 气象站设备数据
     * @return 结果
     */
    public int updateBuDataAws(BuDataAws buDataAws);

    /**
     * 删除气象站设备数据
     *
     * @param id 气象站设备数据主键
     * @return 结果
     */
    public int deleteBuDataAwsById(Long id);

    /**
     * 批量删除气象站设备数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuDataAwsByIds(Long[] ids);
}
