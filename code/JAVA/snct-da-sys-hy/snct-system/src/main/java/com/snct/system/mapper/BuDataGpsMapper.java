package com.snct.system.mapper;

import java.util.List;

import com.snct.system.domain.data.BuDataGps;

/**
 * GPS/北斗设备数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface BuDataGpsMapper {
    /**
     * 查询GPS/北斗设备数据
     *
     * @param id GPS/北斗设备数据主键
     * @return GPS/北斗设备数据
     */
    public BuDataGps selectBuDataGpsById(Long id);

    /**
     * 查询GPS/北斗设备数据列表
     *
     * @param buDataGps GPS/北斗设备数据
     * @return GPS/北斗设备数据集合
     */
    public List<BuDataGps> selectBuDataGpsList(BuDataGps buDataGps);

    /**
     * 新增GPS/北斗设备数据
     *
     * @param buDataGps GPS/北斗设备数据
     * @return 结果
     */
    public int insertBuDataGps(BuDataGps buDataGps);

    /**
     * 修改GPS/北斗设备数据
     *
     * @param buDataGps GPS/北斗设备数据
     * @return 结果
     */
    public int updateBuDataGps(BuDataGps buDataGps);

    /**
     * 删除GPS/北斗设备数据
     *
     * @param id GPS/北斗设备数据主键
     * @return 结果
     */
    public int deleteBuDataGpsById(Long id);

    /**
     * 批量删除GPS/北斗设备数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuDataGpsByIds(Long[] ids);
}
